import { useEffect, useRef, useState, forwardRef, useImperativeHandle, useCallback } from "react";
import "@fontsource/caveat/500.css";
import SignatureCanvas from "react-signature-canvas";
import { Trash2 } from "lucide-react";

const TypedSignatureCanvas = forwardRef(({ value, onChange, placeholder = "Type your name" }, ref) => {
  const canvasRef = useRef(null);
  const [isActive, setIsActive] = useState(false);

  const getSignatureDataURL = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas || !value) return null;
    return canvas.toDataURL("image/png");
  }, [value]);

  useImperativeHandle(
    ref,
    () => ({
      getSignatureDataURL,
    }),
    [getSignatureDataURL],
  );

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    const rect = canvas.getBoundingClientRect();

    // Set canvas size to match display size
    canvas.width = rect.width * window.devicePixelRatio;
    canvas.height = rect.height * window.devicePixelRatio;
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Set font styling to match the input
    ctx.font = "3rem Caveat, cursive";
    ctx.textAlign = "center";
    ctx.textBaseline = "middle";
    ctx.fillStyle = value ? "#1e293b" : "#6b7280"; // slate-800 or gray-500

    // Draw text
    const text = value || placeholder;
    const x = rect.width / 2;
    const y = rect.height / 2 + 38;

    if (!value) {
      // fix initial font loading
      setTimeout(() => {
        ctx.fillText(text, x, y);
      }, 50);
      ctx.fillText(" ", x, y);
    } else {
      ctx.fillText(text, x, y);
    }
  }, [value, placeholder]);

  const handleCanvasClick = () => {
    setIsActive(true);
  };

  const handleKeyDown = (e) => {
    if (e.key === "Escape") {
      setIsActive(false);
      return;
    }

    if (e.key === "Backspace") {
      onChange(value.slice(0, -1));
    } else if (e.key.length === 1) {
      onChange(value + e.key);
    }
  };

  return (
    <div className="relative w-full h-full">
      <canvas
        ref={canvasRef}
        onClick={handleCanvasClick}
        onKeyDown={handleKeyDown}
        tabIndex={0}
        className="w-full h-full cursor-text focus:outline-none"
        style={{ width: "100%", height: "100%" }}
      />
      {isActive && (
        <div className="absolute inset-0 top-10 pointer-events-none">
          <input
            type="text"
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onBlur={() => setIsActive(false)}
            autoFocus
            className="font-caveat font-medium text-5xl w-full h-full bg-transparent text-transparent caret-slate-800 text-center focus:outline-none"
          />
        </div>
      )}
      {value && (
        <button
          type="button"
          onClick={() => onChange("")}
          className="absolute top-3 right-3 z-20  border-gray-300 rounded-full shadow-sm hover:bg-gray-50 transition-colors"
          title="Clear signature"
        >
          <Trash2 className="text-red-600" />
        </button>
      )}
    </div>
  );
});

const SigCanvas = forwardRef((_, ref) => {
  const [isEmpty, setIsEmpty] = useState(true);
  const signCanvasRef = useRef(null);

  const getSignatureDataURL = useCallback(() => {
    if (!signCanvasRef.current || isEmpty) return null;
    return signCanvasRef.current.toDataURL("image/png");
  }, [isEmpty]);

  useImperativeHandle(
    ref,
    () => ({
      getSignatureDataURL,
    }),
    [getSignatureDataURL],
  );

  useEffect(() => {
    const canvasRef = signCanvasRef.current;

    if (canvasRef) {
      canvasRef.clear();

      canvasRef._sigPad.onBegin = () => {
        setIsEmpty(false);
      };

      setIsEmpty(true);
    }

    return () => {
      // canvasRef?.off();
    };
  }, []);

  const clearCanvas = () => {
    if (signCanvasRef.current) {
      signCanvasRef.current.clear();
      setIsEmpty(true);
    }
  };

  return (
    <div className="relative font-caveat text-5xl w-full h-full flex items-center justify-center text-gray-500">
      <SignatureCanvas
        ref={(ref) => {
          signCanvasRef.current = ref;
        }}
        canvasProps={{ className: "relative z-0 bg-white", style: { width: "100%", height: "100%" } }}
        backgroundColor="white"
      />
      {isEmpty && <span className="absolute z-10 left-1/2 top-23  transform -translate-x-1/2 ">Sign here</span>}
      {!isEmpty && (
        <button
          type="button"
          onClick={clearCanvas}
          className="absolute top-3 right-3 z-20  border-gray-300 rounded-full shadow-sm hover:bg-gray-50 transition-colors"
          title="Clear signature"
        >
          <Trash2 className="text-red-600" />
        </button>
      )}
    </div>
  );
});

export default function SignatureMethod() {
  const [typedSignature, setSignature] = useState("");
  const [signatureMethod, setSignatureMethod] = useState("type"); // Track signature method
  const typedCanvasRef = useRef(null);
  const drawCanvasRef = useRef(null);

  const toggleSignatureMethod = () => {
    if (signatureMethod === "type") {
      console.log({ base64: typedCanvasRef.current?.getSignatureDataURL() });
      setSignature("");
    } else {
      console.log({ base64Drawn: drawCanvasRef.current?.getSignatureDataURL() });
    }
    setSignatureMethod(signatureMethod === "type" ? "draw" : "type");
  };

  return (
    <div className="space-y-4">
      <div className="w-full">
        <label className="block text-sm font-bold text-slate-800 mb-3">Signature: </label>

        {/* Signature Display Area */}
        <div className="w-full min-w-[350px] lg:w-1/2 h-42 relative border-2 border-slate-300 rounded-lg bg-white mb-4 flex items-center justify-center">
          <div id="signature-box" className="relative z-10  w-full h-full flex items-center justify-center ">
            {signatureMethod === "type" ? (
              <TypedSignatureCanvas
                ref={typedCanvasRef}
                value={typedSignature}
                onChange={setSignature}
                placeholder="Type your name"
              />
            ) : (
              <SigCanvas ref={drawCanvasRef} />
            )}
            {/** sign on line */}
            <div className="absolute w-2/3 h-0.5 bottom-8 border-t border-1/2 border-dashed border-gray-400 z-[1]" />
          </div>
        </div>

        {/* Signature Method Buttons */}
        <div className="w-full lg:w-1/2 lg:min-w-[350px] flex gap-4">
          <button
            type="button"
            onClick={toggleSignatureMethod}
            className={`flex-1 py-3 px-6 rounded-lg border-2 font-medium transition-colors ${
              signatureMethod === "type"
                ? "border-blue-500 bg-blue-50 text-blue-700"
                : "border-slate-300 bg-white text-slate-700 hover:bg-slate-50"
            }`}
          >
            Type
          </button>
          <button
            type="button"
            onClick={toggleSignatureMethod}
            className={`flex-1 py-3 px-6 rounded-lg border-2 font-medium transition-colors ${
              signatureMethod === "draw"
                ? "border-blue-500 bg-blue-50 text-blue-700"
                : "border-slate-300 bg-white text-slate-700 hover:bg-slate-50"
            }`}
          >
            Draw
          </button>
        </div>
      </div>
      <div className="w-full max-w-xs">
        <label className="block text-sm font-bold text-slate-800 mb-2">Date of Signing: </label>
        <div className="w-full p-3 border border-slate-300 rounded-lg text-sm font-semibold text-slate-800 shadow-sm">
          {new Date().toLocaleDateString("en-US", {
            year: "numeric",
            month: "long",
            day: "numeric",
          })}
        </div>
      </div>
    </div>
  );
}
